import { api } from './api';

// Define types
export interface Session {
  id: string;
  userId: number;
  examId: number;
  start_time: string;
  end_time: string;
  status: string;
  score: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface CreateSessionDto {
  userId: number;
  examId: number;
  start_time: string;
  end_time: string;
  status: string;
  score: number;
}

export interface UpdateSessionDto {
  userId?: number;
  examId?: number;
  start_time?: string;
  end_time?: string;
  status?: string;
  score?: number;
}

// Session service for handling session-related API calls
export const sessionService = {
  // Get all sessions
  async getSessions(): Promise<Session[]> {
    const response = await api.get<Session[]>('/sessions');
    return response.data;
  },

  // Get sessions for a specific user
  async getUserSessions(userId: string): Promise<Session[]> {
    const response = await api.get<Session[]>(`/sessions/user/${userId}`);
    return response.data;
  },

  // Get a specific session
  async getSession(id: string): Promise<Session> {
    const response = await api.get<Session>(`/sessions/${id}`);
    return response.data;
  },

  // Create a new session
  async createSession(sessionData: CreateSessionDto): Promise<Session> {
    const response = await api.post<Session>('/sessions', sessionData);
    return response.data;
  },

  // Update a session
  async updateSession(id: string, sessionData: UpdateSessionDto): Promise<Session> {
    const response = await api.patch<Session>(`/sessions/${id}`, sessionData);
    return response.data;
  },

  // Delete a session
  async deleteSession(id: string): Promise<void> {
    await api.delete(`/sessions/${id}`);
  },

  // Get answers for a session
  async getSessionAnswers(id: string): Promise<any[]> { // Assuming 'any[]' for now, will define Answer interface later
    const response = await api.get<any[]>(`/sessions/${id}/answers`);
    return response.data;
  },

  // Complete an exam session and calculate score
  async completeSession(id: string): Promise<Session> {
    const response = await api.patch<Session>(`/sessions/${id}/complete`);
    return response.data;
  },

  // Recalculate score for a completed session
  async recalculateScore(id: string): Promise<Session> {
    const response = await api.patch<Session>(`/sessions/${id}/recalculate-score`);
    return response.data;
  }
};
