import { api } from './api';

// Define types
export interface Question {
  id: string;
  question_text: string;
  chapter_id: number;
  createdAt?: string;
}

export interface CreateQuestionDto {
  question_text: string;
  chapter_id: number;
}

export interface UpdateQuestionDto {
  question_text?: string;
  chapter_id?: number;
}

// Question service for handling question-related API calls
export const questionService = {
  // Get all questions
  async getQuestions(): Promise<Question[]> {
    const response = await api.get<Question[]>('/questions');
    return response.data;
  },

  // Get a specific question
  async getQuestion(id: string): Promise<Question> {
    const response = await api.get<Question>(`/questions/${id}`);
    return response.data;
  },

  // Create a new question
  async createQuestion(questionData: CreateQuestionDto): Promise<Question> {
    const response = await api.post<Question>('/questions', questionData);
    return response.data;
  },

  // Update a question
  async updateQuestion(id: string, questionData: UpdateQuestionDto): Promise<Question> {
    const response = await api.patch<Question>(`/questions/${id}`, questionData);
    return response.data;
  },

  // Delete a question
  async deleteQuestion(id: string): Promise<void> {
    await api.delete(`/questions/${id}`);
  },

  // Get options for a question
  async getQuestionOptions(id: string): Promise<any[]> { // Assuming 'any[]' for now, will define Option interface later
    const response = await api.get<any[]>(`/questions/${id}/options`);
    return response.data;
  }
};
