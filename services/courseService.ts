import { api } from './api';

// Define types
export interface Course {
  id: string;
  name: string;
  description: string;
  coefficient: number;
  negative_marking_factor: number;
  course_group_id: number;
  createdAt?: string;
}

export interface CreateCourseDto {
  name: string;
  description: string;
  coefficient: number;
  negative_marking_factor: number;
  course_group_id: number;
}

export interface UpdateCourseDto {
  name?: string;
  description?: string;
  coefficient?: number;
  negative_marking_factor?: number;
  course_group_id?: number;
}

// Course service for handling course-related API calls
export const courseService = {
  // Get all courses
  async getCourses(): Promise<Course[]> {
    const response = await api.get<Course[]>('/courses');
    return response.data;
  },

  // Get a specific course
  async getCourse(id: string): Promise<Course> {
    const response = await api.get<Course>(`/courses/${id}`);
    return response.data;
  },

  // Create a new course
  async createCourse(courseData: CreateCourseDto): Promise<Course> {
    const response = await api.post<Course>('/courses', courseData);
    return response.data;
  },

  // Update a course
  async updateCourse(id: string, courseData: UpdateCourseDto): Promise<Course> {
    const response = await api.patch<Course>(`/courses/${id}`, courseData);
    return response.data;
  },

  // Delete a course
  async deleteCourse(id: string): Promise<void> {
    await api.delete(`/courses/${id}`);
  },

  // Get chapters for a course
  async getCourseChapters(id: string): Promise<any[]> { // Assuming 'any[]' for now, will define Chapter interface later
    const response = await api.get<any[]>(`/courses/${id}/chapters`);
    return response.data;
  },

  // Get majors for a course
  async getCourseMajors(id: string): Promise<any[]> { // Assuming 'any[]' for now, will define Major interface later
    const response = await api.get<any[]>(`/courses/${id}/majors`);
    return response.data;
  }
};
