import { api } from './api';

// Define types
export interface Chapter {
  id: string;
  name: string;
  description: string;
  order_in_course: number;
  course_id: number;
  createdAt?: string;
}

export interface CreateChapterDto {
  name: string;
  description: string;
  order_in_course: number;
  course_id: number;
}

export interface UpdateChapterDto {
  name?: string;
  description?: string;
  order_in_course?: number;
  course_id?: number;
}

// Chapter service for handling chapter-related API calls
export const chapterService = {
  // Get all chapters
  async getChapters(): Promise<Chapter[]> {
    const response = await api.get<Chapter[]>('/chapters');
    return response.data;
  },

  // Get a specific chapter
  async getChapter(id: string): Promise<Chapter> {
    const response = await api.get<Chapter>(`/chapters/${id}`);
    return response.data;
  },

  // Create a new chapter
  async createChapter(chapterData: CreateChapterDto): Promise<Chapter> {
    const response = await api.post<Chapter>('/chapters', chapterData);
    return response.data;
  },

  // Update a chapter
  async updateChapter(id: string, chapterData: UpdateChapterDto): Promise<Chapter> {
    const response = await api.patch<Chapter>(`/chapters/${id}`, chapterData);
    return response.data;
  },

  // Delete a chapter
  async deleteChapter(id: string): Promise<void> {
    await api.delete(`/chapters/${id}`);
  },

  // Get questions for a chapter
  async getChapterQuestions(id: string): Promise<any[]> { // Assuming 'any[]' for now, will define Question interface later
    const response = await api.get<any[]>(`/chapters/${id}/questions`);
    return response.data;
  },

  // Get the course for a chapter
  async getChapterCourse(id: string): Promise<any> { // Assuming 'any' for now, will define Course interface later
    const response = await api.get<any>(`/chapters/${id}/course`);
    return response.data;
  }
};
